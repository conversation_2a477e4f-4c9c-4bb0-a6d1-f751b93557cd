/* src/conf.h.os2.  Manually written by <PERSON>. */

#ifndef _CONF_H_
#define _CONF_H_

/* Define if we are compiling under OS2 */
#define CIRCLE_OS2
 
/* Define if we're compiling CircleMUD under any type of UNIX system */
/* #undef CIRCLE_UNIX */

/* Define to empty if the keyword does not work.  */
#undef const

/* Define if you have <sys/wait.h> that is POSIX.1 compatible.  */
#define HAVE_SYS_WAIT_H

/* Define to `int' if <sys/types.h> doesn't define.  */
#undef pid_t

/* Define as the return type of signal handlers (int or void).  */
#define RETSIGTYPE

/* Define to `unsigned' if <sys/types.h> doesn't define.  */
#undef size_t

/* Define to `int' if <sys/socket.h> doesn't define.  */
#define socklen_t int

/* Define if you have the ANSI C header files.  */
#define STDC_HEADERS

/* Define if you can safely include both <sys/time.h> and <time.h>.  */
#define TIME_WITH_SYS_TIME

/* Define if you have the crypt function.  */
#undef CIRCLE_CRYPT

/* Define if you have the random function. (-lbsd)  */
#define HAVE_RANDOM 1

/* Define if you have the <arpa/telnet.h> header file.  */
#define HAVE_ARPA_TELNET_H

/* Define if you have the <assert.h> header file.  */
#define HAVE_ASSERT_H

/* Define if you have the <crypt.h> header file.  */
#undef HAVE_CRYPT_H

/* Define if you have the <errno.h> header file.  */
#define HAVE_ERRNO_H

/* Define if you have the <fcntl.h> header file.  */
#define HAVE_FCNTL_H

/* Define if you have the <limits.h> header file.  */
#define HAVE_LIMITS_H

/* Define if you have the <memory.h> header file.  */
#define HAVE_MEMORY_H

/* Define if you have the <net/errno.h> header file.  */
#undef HAVE_NET_ERRNO_H

/* Define if you have the <string.h> header file.  */
#define HAVE_STRING_H

/* Define if you have the <sys/fcntl.h> header file.  */
#define HAVE_SYS_FCNTL_H

/* Define if you have the <sys/select.h> header file.  */
#define HAVE_SYS_SELECT_H

/* Define if you have the <sys/time.h> header file.  */
#define HAVE_SYS_TIME_H

/* Define if you have the <sys/types.h> header file.  */
#define HAVE_SYS_TYPES_H 1

/* Define if you have the <unistd.h> header file.  */
#define HAVE_UNISTD_H

/* Define if you have the crypt library (-lcrypt).  */
#undef HAVE_LIBCRYPT

/* Define if you have the malloc library (-lmalloc).  */
#undef HAVE_LIBMALLOC

/* Define if you have the nsl library (-lnsl).  */
#undef HAVE_LIBNSL

/* Define if you have the socket library (-lsocket).  */
#define HAVE_LIBSOCKET

/* Define if your compiler does not prototype remove().  */
/* #undef NEED_REMOVE_PROTO */

/* Define if your compiler does not prototype strerror().  */
/* #undef NEED_STRERROR_PROTO */

/* Define if you have 'struct in_addr' */
#define HAVE_STRUCT_IN_ADDR 1

/* Define if your crypt isn't safe with only 10 characters. */
#undef HAVE_UNSAFE_CRYPT

#endif /* _CONF_H_ */
