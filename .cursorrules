# Luminari MUD Development Rules

## Project Context
This is Luminari MUD, a CircleMUD-based Multi-User Dungeon game written in C. The codebase follows traditional MUD architecture patterns with rooms, objects, mobiles (NPCs), and players interacting in a persistent world.

## Key Architecture Components
- **Structs**: Core data structures in `structs.h` define chars, rooms, objects
- **Commands**: Player commands in `act.*.c` files and `interpreter.c`
- **World Data**: Rooms, objects, mobiles defined in data files (ignored by cursor)
- **OLC**: Online Creation system for building world content
- **DG Scripts**: Triggers and scripting system for dynamic content
- **Combat**: Turn-based combat system in `fight.c`
- **Spells/Skills**: Magic and ability systems in `spells.c`, `feats.c`, etc.

## Coding Conventions
- Use K&R C style with 2-space indentation
- Function names use snake_case (e.g., `do_command`, `char_from_room`)
- Constants and macros use SCREAMING_SNAKE_CASE
- Global variables often prefixed with scope (e.g., `world`, `ch`, `obj`)
- Memory management is critical - always free what you allocate
- Use CircleMUD's built-in memory functions when available

## Important Patterns
- **Character references**: Use `struct char_data *ch` for characters
- **Room references**: Use `struct room_data` and room vnums
- **Object references**: Use `struct obj_data *obj`
- **Command handlers**: Format `ACMD(do_command_name)`
- **Special procedures**: Format `SPECIAL(proc_name)`
- **Safety checks**: Always validate pointers and check for NULL

## File Organization
- **act.*.c**: Player action commands by category
- ***.h**: Header files with function prototypes and constants  
- **spec_*.c**: Special procedures and unique behaviors
- ***edit.c**: Online creation (OLC) editors
- **util/**: Utilities and helper programs
- **documentation/**: Project documentation and guides

## Development Guidelines
- When adding new commands, update `interpreter.c` command table
- New spells need entries in `spells.c` and spell tables
- Always consider multi-player impact and edge cases
- Test changes thoroughly - MUDs are complex systems
- Follow existing error handling patterns
- Use appropriate logging levels for debugging

## Common Tasks
- Adding commands: Create handler in appropriate `act.*.c` file
- Creating spells: Add to spell system with proper school/level
- Building areas: Use OLC or create data files
- Adding features: Consider impact on existing systems
- Bug fixes: Check for similar patterns elsewhere in code

## Security Considerations
- Validate all user input to prevent exploits
- Check permission levels for admin commands
- Sanitize strings to prevent buffer overflows
- Audit file operations and database queries

## Performance Notes
- MUDs run continuously with many connected players
- Optimize loops that run frequently (like combat rounds)
- Be mindful of memory leaks in long-running processes
- Consider caching for expensive operations

## When in Doubt
- Look for similar existing implementations in the codebase
- Check CircleMUD documentation for standard patterns
- Consider backwards compatibility with existing world data
- Ask about design decisions that affect multiple systems 