TIMEDATE=$(date +"%m-%d-%Y-%H-%M")
mv /home/<USER>/dev/bin/circle /home/<USER>/dev/bin/circle.$TIMEDATE
cp ../bin/circle /home/<USER>/dev/bin
echo "Copied binary to luminari dev port"
cp /home/<USER>/dev/changelog /home/<USER>/dev/lib/text/news
echo "Moved changelog over to news file"
echo "3 second delay before starting server..."
sleep 3
echo "Starting dev server on port 4101"
cd /home/<USER>/dev && ./checkmud.sh &
echo ""
echo "To change to the dev directory, run:"
echo "cd /home/<USER>/dev"
