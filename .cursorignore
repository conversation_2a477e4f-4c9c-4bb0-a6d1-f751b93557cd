# Build artifacts and binaries
*.o
*.a
*.so
*.dll
*.exe
*.out
bin/
build/
dist/
Debug/
Release/

# Compiled executables (common MUD names)
circle
luminari
mud
autorun

# Core dumps and debugging
core
core.*
*.core
vgcore.*
*.dSYM/

# Log files
*.log
log/
logs/
syslog.*
*.err

# Player and world data files
lib/
data/
playerfiles/
world/
*.pfl
*.plr
*.objs
*.wld
*.mob
*.obj
*.zon
*.shp
*.qst

# Mail and message files
mail/
*.mail

# Backup files
*.bak
*.backup
*.orig
*.save
*~
.#*
#*#

# MySQL/Database related
mysql/
*.sql
*.db
*.sqlite
*.sqlite3

# Configuration files (may contain sensitive data)
config
config.*
*.conf
campaign.h
mud_options.h
mysql_config
vnums.h

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*.tmp
.DS_Store
Thumbs.db

# Generated documentation
documentation/doxygen/
doc/html/
*.dox

# Dependency files
depend
*.d

# Archive and compressed files
*.tar
*.gz
*.zip
*.rar
*.7z

# Temporary files
tmp/
temp/
*.temp

# System files
.git/
*.pid

# Large binary assets
*.wav
*.mp3
*.ogg
*.png
*.jpg
*.jpeg
*.gif
*.bmp

# Old documentation and examples
documentation/old_doc/
*.example.* 